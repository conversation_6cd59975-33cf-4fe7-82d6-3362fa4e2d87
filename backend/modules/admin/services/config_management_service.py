"""
YAML配置文件管理服务
提供统一的配置文件读取、更新、验证功能
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import shutil
import re

logger = logging.getLogger(__name__)

class YAMLConfigManager:
    """YAML配置文件管理器"""

    # 支持的配置文件映射
    CONFIG_FILES = {
        'user_behavior': {
            'path': 'backend/config/user_behavior_config.yml',
            'description': '用户行为分析配置',
            'main_section': 'user_behavior_analysis'
        },
        'agent_filter': {
            'path': 'backend/config/agent_data_filter_config.yml',
            'description': '代理商数据过滤配置',
            'main_section': 'data_quality_filters'
        },

    }

    # 字段中文映射配置
    FIELD_TRANSLATIONS = {
        'user_behavior': {
            'user_behavior_analysis': '用户行为分析',
            'thresholds': '阈值配置',
            'min_total_volume': '最小总交易量 (USDT)',
            'min_trades_count': '最小交易笔数',
            'analysis_time_window_days': '分析时间窗口 (天)',
            'fund_scale_categories': '资金规模分类',
            'new_user_criteria': '新用户判定标准',
            'max_trading_days': '最大交易天数',
            'max_trades_count': '最大交易笔数',
            'scoring_weights': '专业度评分权重',
            'profitability': '盈利能力权重',
            'risk_control': '风险控制权重',
            'trading_behavior': '交易行为权重',
            'market_understanding': '市场理解权重',
            'coin_win_rate_analysis': '币种胜率分析配置',
            'enabled': '启用',
            'min_trades_threshold': '最小交易笔数阈值',
            'min_volume_threshold': '最小交易量阈值 (USDT)',
            'significance_threshold': '显著性阈值',
            'expertise_levels': '专业程度分级标准',
            'expert': '专家级',
            'skilled': '熟练级',
            'average': '一般级',
            'weak': '弱势',
            'min_win_rate': '最小胜率',
            'min_trades': '最小交易笔数',
            'min_volume': '最小交易量',
            'max_win_rate': '最大胜率',
            'advantage_identification': '优势币种识别标准',
            'high_win_rate': '高胜率阈值',
            'high_profit_factor': '高盈利因子阈值',
            'min_statistical_confidence': '最小统计置信度',
            'ranking_weights': '币种排名权重',
            'win_rate': '胜率权重',
            'profit_factor': '盈利因子权重',
            'volume': '交易量权重',
            'trade_count': '交易笔数权重',
            'abnormal_weights': '异常交易权重配置',
            'wash_trading': '对敲交易权重',
            'high_frequency': '高频交易权重',
            'funding_arbitrage': '资金费率套利权重',
            'large_trader_analysis': '大户交易行为增强分析',
            'min_volume_threshold': '大户最小交易量阈值',
            'analyze_trade_frequency': '分析单次交易频率',
            'complex_strategy_detection': '复杂策略检测',
            'dynamic_classification': '币种分类动态配置',
            'fallback_category': '未分类币种归类',
            'auto_update_enabled': '自动更新启用',
            'validation': '验证配置',
            'sample_sizes': '样本大小',
            'professional_traders': '专业交易者',
            'semi_professional': '半专业交易者',
            'retail_traders': '散户交易者',
            'new_users': '新用户',
            'accuracy_thresholds': '准确度阈值',
            'professional_precision': '专业精度',
            'retail_recall': '散户召回率',
            'new_user_accuracy': '新用户准确度',
            'overall_accuracy': '整体准确度',
            'stability_thresholds': '稳定性阈值',
            'monthly_score_volatility': '月度评分波动性',
            'abnormal_score_ratio': '异常评分比例',
            'business_correlation': '业务相关性',
            'cache': '缓存配置',
            'default_ttl': '默认TTL',
            'redis_url': 'Redis URL',
            'logging': '日志配置',
            'level': '日志级别',
            'file_path': '文件路径',
            'max_file_size': '最大文件大小',
            'backup_count': '备份数量'
        },
        'agent_filter': {
            'data_quality_filters': '数据质量过滤器',
            'internal_operations': '内部运营',
            'description': '描述',
            'enabled': '启用',
            'internal_bd_names': '内部BD名称',
            'internal_ips': '内部IP',
            'internal_user_ids': '内部用户ID',
            'invalid_device_ids': '无效设备ID',
            'case_sensitive': '区分大小写',
            'patterns': '模式',
            'default_settings': '默认设置',
            'exclude_internal_operations': '排除内部运营',
            'exclude_invalid_devices': '排除无效设备',
            'filtering_stats': '过滤统计',
            'log_level': '日志级别',
            'show_detailed_stats': '显示详细统计',
            'advanced_filters': '高级过滤器',
            'anomaly_detection': '异常检测',
            'max_same_device_users': '最大同设备用户数',
            'max_same_ip_users': '最大同IP用户数',
            'suspicious_patterns': '可疑模式',
            'min_data_quality': '最小数据质量',
            'min_device_id_length': '最小设备ID长度',
            'min_ip_segments': '最小IP段数',
            'require_valid_digital_id': '需要有效数字ID'
        },
        'wash_trading': {
            'detection': '检测配置',
            'min_records': '最小记录数',
            'required_fields': '必需字段列表',
            'member_id': '会员ID',
            'digital_id': '数字ID',
            'contract_name': '合约名称',
            'side': '方向',
            'price': '价格',
            'volume': '交易数量',
            'deal_vol_usdt': '交易金额',
            'timestamp': '时间戳',
            'position_id': '持仓ID',
            'time_thresholds': '时间阈值配置',
            'open_time_window': '开仓时间匹配窗口 (秒)',
            'risk_classification': '风险分级',
            'high_risk_close_window_minutes': '高风险平仓间隔 (分钟)',
            'medium_risk_close_window_hours': '中风险平仓间隔 (小时)',
            'amount_matching': '金额匹配配置',
            'relative_tolerance': '相对容差 (百分比)',
            'absolute_tolerance': '绝对容差',
            'same_account': '同账户对敲配置',
            'enabled': '启用',
            'cross_account': '跨账户对敲配置',
            'time_window': '时间窗口 (秒)',
            'check_bd_relationship': '检查BD关系',
            'network_analysis': '网络分析配置',
            'storage': '数据存储配置',
            'save_risk_levels': '保存的风险等级',
            'validation': '验证与质量控制',
            'require_position_id': '需要持仓ID',
            'business_rules': '业务规则配置',
            'min_trade_amount': '最小交易金额阈值',
            'frequency_check': '频率检查配置',
            'min_wash_count': '最小对敲次数',
            'time_window_hours': '时间窗口 (小时)'
        }
    }
    
    def __init__(self, project_root: str = None):
        """初始化配置管理器"""
        if project_root is None:
            # 自动检测项目根目录
            current_file = os.path.abspath(__file__)
            project_root = current_file
            while project_root != '/' and project_root != os.path.dirname(project_root):
                if os.path.exists(os.path.join(project_root, 'manage_duckdb.sh')):
                    break
                project_root = os.path.dirname(project_root)
        
        self.project_root = project_root
        self.backup_dir = os.path.join(project_root, 'temp', 'config_backups')
        os.makedirs(self.backup_dir, exist_ok=True)
        logger.info(f"配置管理器初始化完成，项目根目录: {self.project_root}")

    def _translate_field_name(self, config_type: str, field_name: str) -> str:
        """将英文字段名翻译为中文显示名"""
        translations = self.FIELD_TRANSLATIONS.get(config_type, {})
        return translations.get(field_name, field_name)

    def _add_field_translations(self, config_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """为配置数据添加字段翻译信息"""
        if not isinstance(data, dict):
            return data

        translated_data = {}
        for key, value in data.items():
            # 添加原始字段
            translated_data[key] = value

            # 如果是字典，递归处理
            if isinstance(value, dict):
                translated_data[key] = self._add_field_translations(config_type, value)

            # 添加翻译字段（以_display结尾）
            chinese_name = self._translate_field_name(config_type, key)
            if chinese_name != key:  # 只有当翻译存在时才添加
                translated_data[f"{key}_display"] = chinese_name

        return translated_data

    def get_config_file_path(self, config_type: str) -> str:
        """获取配置文件的完整路径"""
        if config_type not in self.CONFIG_FILES:
            raise ValueError(f"不支持的配置类型: {config_type}")
        
        relative_path = self.CONFIG_FILES[config_type]['path']
        return os.path.join(self.project_root, relative_path)
    
    def load_config(self, config_type: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = self.get_config_file_path(config_type)

            if not os.path.exists(config_path):
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 添加字段翻译信息
            translated_data = self._add_field_translations(config_type, config_data)

            logger.info(f"成功加载配置文件: {config_type}")
            return {
                'config_type': config_type,
                'description': self.CONFIG_FILES[config_type]['description'],
                'data': translated_data,
                'file_path': config_path,
                'last_modified': datetime.fromtimestamp(os.path.getmtime(config_path)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_type}: {str(e)}")
            raise
    
    def save_config(self, config_type: str, config_data: Dict[str, Any], 
                   create_backup: bool = True) -> Dict[str, Any]:
        """保存配置文件"""
        try:
            config_path = self.get_config_file_path(config_type)
            
            # 验证配置数据
            self._validate_config(config_type, config_data)
            
            # 创建备份
            if create_backup and os.path.exists(config_path):
                self._create_backup(config_type, config_path)
            
            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"成功保存配置文件: {config_type}")
            
            return {
                'status': 'success',
                'message': f'配置文件 {config_type} 保存成功',
                'config_type': config_type,
                'file_path': config_path,
                'saved_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_type}: {str(e)}")
            raise
    
    def _validate_config(self, config_type: str, config_data: Dict[str, Any]):
        """验证配置数据格式"""
        if config_type == 'user_behavior':
            self._validate_user_behavior_config(config_data)
        elif config_type == 'agent_filter':
            self._validate_agent_filter_config(config_data)
        elif config_type == 'wash_trading':
            self._validate_wash_trading_config(config_data)
    
    def _validate_user_behavior_config(self, config_data: Dict[str, Any]):
        """验证用户行为分析配置"""
        required_sections = ['user_behavior_analysis']
        for section in required_sections:
            if section not in config_data:
                raise ValueError(f"缺少必需的配置节: {section}")
        
        # 验证关键配置项
        uba_config = config_data['user_behavior_analysis']
        if 'thresholds' not in uba_config:
            raise ValueError("缺少thresholds配置")
        
        # 验证数值范围
        thresholds = uba_config['thresholds']
        if 'min_total_volume' in thresholds:
            if not isinstance(thresholds['min_total_volume'], (int, float)) or thresholds['min_total_volume'] < 0:
                raise ValueError("min_total_volume必须是非负数")
    
    def _validate_agent_filter_config(self, config_data: Dict[str, Any]):
        """验证代理商过滤配置"""
        required_sections = ['data_quality_filters']
        for section in required_sections:
            if section not in config_data:
                raise ValueError(f"缺少必需的配置节: {section}")
    
    def _validate_wash_trading_config(self, config_data: Dict[str, Any]):
        """验证对敲交易检测配置"""
        required_sections = ['detection']
        for section in required_sections:
            if section not in config_data:
                raise ValueError(f"缺少必需的配置节: {section}")
    
    def _create_backup(self, config_type: str, config_path: str):
        """创建配置文件备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{config_type}_backup_{timestamp}.yml"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        shutil.copy2(config_path, backup_path)
        logger.info(f"创建配置备份: {backup_path}")
    
    def list_all_configs(self) -> List[Dict[str, Any]]:
        """列出所有支持的配置文件"""
        configs = []
        for config_type, info in self.CONFIG_FILES.items():
            try:
                config_path = self.get_config_file_path(config_type)
                exists = os.path.exists(config_path)
                last_modified = None
                
                if exists:
                    last_modified = datetime.fromtimestamp(
                        os.path.getmtime(config_path)
                    ).isoformat()
                
                configs.append({
                    'config_type': config_type,
                    'description': info['description'],
                    'file_path': config_path,
                    'exists': exists,
                    'last_modified': last_modified
                })
            except Exception as e:
                logger.error(f"检查配置文件状态失败 {config_type}: {str(e)}")
                configs.append({
                    'config_type': config_type,
                    'description': info['description'],
                    'exists': False,
                    'error': str(e)
                })
        
        return configs
    
    def get_backup_list(self, config_type: str = None) -> List[Dict[str, Any]]:
        """获取备份文件列表"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.yml') and 'backup' in filename:
                if config_type and not filename.startswith(config_type):
                    continue
                
                backup_path = os.path.join(self.backup_dir, filename)
                backups.append({
                    'filename': filename,
                    'path': backup_path,
                    'created_at': datetime.fromtimestamp(
                        os.path.getctime(backup_path)
                    ).isoformat(),
                    'size': os.path.getsize(backup_path)
                })
        
        # 按创建时间倒序排列
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        return backups

# 创建全局配置管理器实例
config_manager = YAMLConfigManager()
